const mongoose = require('mongoose');

const conversationSchema = new mongoose.Schema({
  participants: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    role: {
      type: String,
      enum: ['doctor', 'patient'],
      required: true
    },
    joinedAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Quick access to participant IDs for efficient querying
  doctorId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  
  patientId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  
  lastMessage: {
    content: String,
    sender: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    timestamp: {
      type: Date,
      default: Date.now
    }
  },
  
  // Track unread message counts for each participant
  unreadCounts: {
    doctor: {
      type: Number,
      default: 0
    },
    patient: {
      type: Number,
      default: 0
    }
  },
  
  isActive: {
    type: Boolean,
    default: true
  },
  
  createdAt: {
    type: Date,
    default: Date.now
  },
  
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Compound index for efficient participant queries
conversationSchema.index({ doctorId: 1, patientId: 1 }, { unique: true });

// Update the updatedAt field before saving
conversationSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Static method to find or create conversation
conversationSchema.statics.findOrCreateConversation = async function(doctorId, patientId) {
  let conversation = await this.findOne({ doctorId, patientId });
  
  if (!conversation) {
    conversation = new this({
      doctorId,
      patientId,
      participants: [
        { user: doctorId, role: 'doctor' },
        { user: patientId, role: 'patient' }
      ]
    });
    await conversation.save();
  }
  
  return conversation;
};

// Instance method to increment unread count
conversationSchema.methods.incrementUnreadCount = function(userRole) {
  if (userRole === 'doctor') {
    this.unreadCounts.patient += 1;
  } else if (userRole === 'patient') {
    this.unreadCounts.doctor += 1;
  }
  return this.save();
};

// Instance method to reset unread count
conversationSchema.methods.resetUnreadCount = function(userRole) {
  if (userRole === 'doctor') {
    this.unreadCounts.doctor = 0;
  } else if (userRole === 'patient') {
    this.unreadCounts.patient = 0;
  }
  return this.save();
};

const Conversation = mongoose.model('Conversation', conversationSchema);
module.exports = Conversation;
