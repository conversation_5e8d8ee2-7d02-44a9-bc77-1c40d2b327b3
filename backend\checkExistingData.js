const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const User = require('./models/User');
const Patient = require('./models/doctor/Patient');
const Message = require('./models/doctor/Message');

const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URI);
    console.log('MongoDB connected!');
  } catch (error) {
    console.log('MongoDB connection failed:', error);
    process.exit(1);
  }
};

const checkExistingData = async () => {
  try {
    console.log('=== Checking Existing Database Data ===\n');

    // Check Users
    const doctors = await User.find({ role: 'doctor' }).select('name email');
    const patients = await User.find({ role: 'patient' }).select('name email');
    
    console.log(`👨‍⚕️ Doctors in database: ${doctors.length}`);
    doctors.slice(0, 5).forEach(doctor => {
      console.log(`  - ${doctor.name} (${doctor.email})`);
    });
    if (doctors.length > 5) console.log(`  ... and ${doctors.length - 5} more`);

    console.log(`\n🏥 Patients (Users) in database: ${patients.length}`);
    patients.slice(0, 5).forEach(patient => {
      console.log(`  - ${patient.name} (${patient.email})`);
    });
    if (patients.length > 5) console.log(`  ... and ${patients.length - 5} more`);

    // Check Patient records
    const patientRecords = await Patient.find().select('name contact assignedDoctorIds');
    console.log(`\n📋 Patient Records in database: ${patientRecords.length}`);
    
    for (const patient of patientRecords.slice(0, 5)) {
      const assignedDoctors = await User.find({ 
        _id: { $in: patient.assignedDoctorIds } 
      }).select('name');
      
      console.log(`  - ${patient.name} (${patient.contact?.email || 'No email'})`);
      console.log(`    Assigned to: ${assignedDoctors.map(d => d.name).join(', ') || 'No doctors'}`);
    }
    if (patientRecords.length > 5) console.log(`  ... and ${patientRecords.length - 5} more`);

    // Check existing Messages
    const messages = await Message.find().populate('senderId recipientId', 'name email');
    console.log(`\n💬 Existing Messages: ${messages.length}`);
    messages.slice(0, 3).forEach(msg => {
      console.log(`  - From: ${msg.senderId?.name || 'Unknown'} To: ${msg.recipientId?.name || 'Unknown'}`);
      console.log(`    Content: ${msg.content.substring(0, 50)}...`);
    });

    // Find doctor-patient relationships
    console.log(`\n🔗 Doctor-Patient Relationships:`);
    for (const doctor of doctors.slice(0, 3)) {
      const assignedPatients = await Patient.find({ 
        assignedDoctorIds: doctor._id 
      }).select('name contact');
      
      console.log(`\n${doctor.name}:`);
      if (assignedPatients.length > 0) {
        assignedPatients.forEach(patient => {
          console.log(`  - ${patient.name} (${patient.contact?.email || 'No email'})`);
        });
      } else {
        console.log(`  - No patients assigned`);
      }
    }

    console.log('\n=== Summary ===');
    console.log(`Total Doctors: ${doctors.length}`);
    console.log(`Total Patient Users: ${patients.length}`);
    console.log(`Total Patient Records: ${patientRecords.length}`);
    console.log(`Total Messages: ${messages.length}`);

  } catch (error) {
    console.error('Error checking data:', error);
  } finally {
    mongoose.connection.close();
  }
};

const runCheck = async () => {
  await connectDB();
  await checkExistingData();
};

runCheck();
