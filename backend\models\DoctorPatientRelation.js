const mongoose = require('mongoose');

const doctorPatientRelationSchema = new mongoose.Schema({
  doctorId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  
  patientId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  
  // Status of the relationship
  status: {
    type: String,
    enum: ['active', 'inactive', 'pending', 'terminated'],
    default: 'active'
  },
  
  // When the relationship was established
  assignedAt: {
    type: Date,
    default: Date.now
  },
  
  // Who assigned this relationship (admin, self-assignment, etc.)
  assignedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  // Additional metadata
  notes: {
    type: String,
    trim: true
  },
  
  // Permissions for this specific relationship
  permissions: {
    canMessage: {
      type: Boolean,
      default: true
    },
    canViewRecords: {
      type: Boolean,
      default: true
    },
    canScheduleAppointments: {
      type: Boolean,
      default: true
    }
  },
  
  // Relationship metadata
  isPrimary: {
    type: Boolean,
    default: false // Whether this doctor is the primary care physician
  },
  
  specialty: {
    type: String // Specific specialty for this relationship
  },
  
  createdAt: {
    type: Date,
    default: Date.now
  },
  
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Compound index to ensure unique doctor-patient pairs
doctorPatientRelationSchema.index({ doctorId: 1, patientId: 1 }, { unique: true });

// Index for efficient queries
doctorPatientRelationSchema.index({ doctorId: 1, status: 1 });
doctorPatientRelationSchema.index({ patientId: 1, status: 1 });

// Update the updatedAt field before saving
doctorPatientRelationSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Static method to check if doctor can message patient
doctorPatientRelationSchema.statics.canDoctorMessagePatient = async function(doctorId, patientId) {
  const relation = await this.findOne({
    doctorId,
    patientId,
    status: 'active',
    'permissions.canMessage': true
  });
  return !!relation;
};

// Static method to get doctor's patients
doctorPatientRelationSchema.statics.getDoctorPatients = function(doctorId, status = 'active') {
  return this.find({ doctorId, status })
    .populate('patientId', 'name email role')
    .sort({ assignedAt: -1 });
};

// Static method to get patient's doctors
doctorPatientRelationSchema.statics.getPatientDoctors = function(patientId, status = 'active') {
  return this.find({ patientId, status })
    .populate('doctorId', 'name email role')
    .sort({ assignedAt: -1 });
};

// Static method to create or update relationship
doctorPatientRelationSchema.statics.createOrUpdateRelation = async function(doctorId, patientId, options = {}) {
  const existingRelation = await this.findOne({ doctorId, patientId });
  
  if (existingRelation) {
    // Update existing relationship
    Object.assign(existingRelation, options);
    return existingRelation.save();
  } else {
    // Create new relationship
    const newRelation = new this({
      doctorId,
      patientId,
      ...options
    });
    return newRelation.save();
  }
};

// Instance method to activate relationship
doctorPatientRelationSchema.methods.activate = function() {
  this.status = 'active';
  return this.save();
};

// Instance method to deactivate relationship
doctorPatientRelationSchema.methods.deactivate = function() {
  this.status = 'inactive';
  return this.save();
};

// Instance method to terminate relationship
doctorPatientRelationSchema.methods.terminate = function() {
  this.status = 'terminated';
  return this.save();
};

const DoctorPatientRelation = mongoose.model('DoctorPatientRelation', doctorPatientRelationSchema);
module.exports = DoctorPatientRelation;
