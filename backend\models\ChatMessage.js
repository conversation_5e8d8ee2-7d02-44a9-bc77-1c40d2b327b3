const mongoose = require('mongoose');

const chatMessageSchema = new mongoose.Schema({
  conversationId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Conversation',
    required: true,
    index: true
  },
  
  sender: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  
  senderRole: {
    type: String,
    enum: ['doctor', 'patient'],
    required: true
  },
  
  receiver: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  receiverRole: {
    type: String,
    enum: ['doctor', 'patient'],
    required: true
  },
  
  content: {
    type: String,
    required: true,
    trim: true
  },
  
  messageType: {
    type: String,
    enum: ['text', 'image', 'file', 'system'],
    default: 'text'
  },
  
  // For file/image messages
  attachments: [{
    filename: String,
    originalName: String,
    mimetype: String,
    size: Number,
    url: String
  }],
  
  // Message status tracking
  status: {
    type: String,
    enum: ['sent', 'delivered', 'read'],
    default: 'sent'
  },
  
  // Timestamps for message lifecycle
  sentAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  
  deliveredAt: {
    type: Date
  },
  
  readAt: {
    type: Date
  },
  
  // For message editing/deletion
  isEdited: {
    type: Boolean,
    default: false
  },
  
  editedAt: {
    type: Date
  },
  
  isDeleted: {
    type: Boolean,
    default: false
  },
  
  deletedAt: {
    type: Date
  },
  
  // Reply functionality
  replyTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ChatMessage'
  },
  
  // System message data
  systemData: {
    type: mongoose.Schema.Types.Mixed
  }
});

// Compound indexes for efficient querying
chatMessageSchema.index({ conversationId: 1, sentAt: -1 });
chatMessageSchema.index({ sender: 1, sentAt: -1 });
chatMessageSchema.index({ receiver: 1, status: 1 });

// Ensure conversationId is always set before saving
chatMessageSchema.pre('validate', function(next) {
  if (!this.conversationId) {
    return next(new Error('conversationId is required'));
  }
  next();
});

// Instance method to mark as delivered
chatMessageSchema.methods.markAsDelivered = function() {
  if (this.status === 'sent') {
    this.status = 'delivered';
    this.deliveredAt = new Date();
    return this.save();
  }
  return Promise.resolve(this);
};

// Instance method to mark as read
chatMessageSchema.methods.markAsRead = function() {
  if (this.status !== 'read') {
    this.status = 'read';
    this.readAt = new Date();
    if (!this.deliveredAt) {
      this.deliveredAt = new Date();
    }
    return this.save();
  }
  return Promise.resolve(this);
};

// Static method to get conversation messages with pagination
chatMessageSchema.statics.getConversationMessages = function(conversationId, page = 1, limit = 50) {
  const skip = (page - 1) * limit;
  
  return this.find({ 
    conversationId, 
    isDeleted: false 
  })
  .populate('sender', 'name email role')
  .populate('receiver', 'name email role')
  .populate('replyTo', 'content sender sentAt')
  .sort({ sentAt: -1 })
  .skip(skip)
  .limit(limit);
};

// Static method to mark multiple messages as read
chatMessageSchema.statics.markMessagesAsRead = function(conversationId, userId) {
  return this.updateMany(
    { 
      conversationId, 
      receiver: userId, 
      status: { $ne: 'read' } 
    },
    { 
      status: 'read', 
      readAt: new Date() 
    }
  );
};

// Pre-save middleware to update conversation's last message
chatMessageSchema.pre('save', async function(next) {
  if (this.isNew && !this.isDeleted) {
    try {
      const Conversation = mongoose.model('Conversation');
      await Conversation.findByIdAndUpdate(this.conversationId, {
        lastMessage: {
          content: this.content,
          sender: this.sender,
          timestamp: this.sentAt
        },
        updatedAt: new Date()
      });
    } catch (error) {
      console.error('Error updating conversation last message:', error);
    }
  }
  next();
});

const ChatMessage = mongoose.model('ChatMessage', chatMessageSchema);
module.exports = ChatMessage;
