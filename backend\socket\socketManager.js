const { Server } = require('socket.io');
const socketAuth = require('./socketAuth');
const MessageHandlers = require('./messageHandlers');

class SocketManager {
  constructor(server) {
    this.io = new Server(server, {
      cors: {
        origin: process.env.FRONTEND_URL || "http://localhost:5173",
        methods: ["GET", "POST"],
        credentials: true
      },
      transports: ['websocket', 'polling']
    });

    this.messageHandlers = new MessageHandlers(this.io);
    this.setupMiddleware();
    this.setupEventHandlers();
  }

  setupMiddleware() {
    // Authentication middleware
    this.io.use(socketAuth);
  }

  setupEventHandlers() {
    this.io.on('connection', (socket) => {
      console.log(`New socket connection: ${socket.id} for user: ${socket.user.name}`);

      // Handle connection
      this.messageHandlers.handleConnection(socket);

      // Message events
      socket.on('send_message', (data) => {
        this.messageHandlers.handleSendMessage(socket, data);
      });

      socket.on('join_conversation', (data) => {
        this.messageHandlers.handleJoinConversation(socket, data);
      });

      socket.on('leave_conversation', (data) => {
        this.messageHandlers.handleLeaveConversation(socket, data);
      });

      socket.on('mark_as_read', (data) => {
        this.messageHandlers.handleMarkAsRead(socket, data);
      });

      socket.on('get_online_users', () => {
        this.messageHandlers.getOnlineUsersFor(socket);
      });

      // Typing indicators
      socket.on('typing_start', (data) => {
        this.handleTypingStart(socket, data);
      });

      socket.on('typing_stop', (data) => {
        this.handleTypingStop(socket, data);
      });

      // Handle disconnection
      socket.on('disconnect', () => {
        console.log(`Socket disconnected: ${socket.id}`);
        this.messageHandlers.handleDisconnection(socket);
      });

      // Error handling
      socket.on('error', (error) => {
        console.error(`Socket error for ${socket.id}:`, error);
      });
    });
  }

  handleTypingStart(socket, data) {
    const { conversationId } = data;
    socket.to(`conversation_${conversationId}`).emit('user_typing', {
      userId: socket.userId,
      userName: socket.user.name,
      conversationId
    });
  }

  handleTypingStop(socket, data) {
    const { conversationId } = data;
    socket.to(`conversation_${conversationId}`).emit('user_stopped_typing', {
      userId: socket.userId,
      conversationId
    });
  }

  // Method to send message to specific user
  sendToUser(userId, event, data) {
    const socketId = this.messageHandlers.connectedUsers.get(userId);
    if (socketId) {
      this.io.to(socketId).emit(event, data);
      return true;
    }
    return false;
  }

  // Method to send message to conversation
  sendToConversation(conversationId, event, data) {
    this.io.to(`conversation_${conversationId}`).emit(event, data);
  }

  // Method to broadcast to role
  broadcastToRole(role, event, data) {
    this.io.to(`role_${role}`).emit(event, data);
  }

  // Get connected users count
  getConnectedUsersCount() {
    return this.messageHandlers.connectedUsers.size;
  }

  // Get connected users by role
  getConnectedUsersByRole(role) {
    const users = [];
    this.messageHandlers.connectedUsers.forEach((socketId, userId) => {
      const socket = this.io.sockets.sockets.get(socketId);
      if (socket && socket.userRole === role) {
        users.push({
          userId,
          userName: socket.user.name,
          socketId
        });
      }
    });
    return users;
  }

  // Check if user is online
  isUserOnline(userId) {
    return this.messageHandlers.connectedUsers.has(userId);
  }
}

module.exports = SocketManager;
