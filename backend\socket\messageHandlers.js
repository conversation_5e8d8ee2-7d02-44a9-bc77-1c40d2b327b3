const ChatMessage = require('../models/ChatMessage');
const Conversation = require('../models/Conversation');
const DoctorPatientRelation = require('../models/DoctorPatientRelation');
const Patient = require('../models/doctor/Patient');
const User = require('../models/User');

class MessageHandlers {
  constructor(io) {
    this.io = io;
    this.connectedUsers = new Map(); // userId -> socketId mapping
  }

  // Handle user connection
  async handleConnection(socket) {
    const userId = socket.userId;
    const userRole = socket.userRole;
    
    // Store user connection
    this.connectedUsers.set(userId, socket.id);
    
    // Join user to their personal room
    socket.join(`user_${userId}`);
    
    // Join user to role-based room
    socket.join(`role_${userRole}`);
    
    console.log(`User ${userId} (${userRole}) connected with socket ${socket.id}`);
    
    // Emit online status to relevant users
    await this.broadcastUserStatus(userId, userRole, 'online');
    
    // Send user their conversations
    await this.sendUserConversations(socket);
  }

  // Handle user disconnection
  async handleDisconnection(socket) {
    const userId = socket.userId;
    const userRole = socket.userRole;
    
    // Remove user connection
    this.connectedUsers.delete(userId);
    
    console.log(`User ${userId} (${userRole}) disconnected`);
    
    // Emit offline status to relevant users
    await this.broadcastUserStatus(userId, userRole, 'offline');
  }

  // Handle sending a message
  async handleSendMessage(socket, data) {
    try {
      const { receiverId, content, messageType = 'text', replyTo } = data;
      const senderId = socket.userId;
      const senderRole = socket.userRole;
      
      // Validate required fields
      if (!receiverId || !content?.trim()) {
        socket.emit('message_error', { error: 'Missing required fields' });
        return;
      }

      // Get receiver information
      const receiver = await User.findById(receiverId);
      if (!receiver) {
        socket.emit('message_error', { error: 'Receiver not found' });
        return;
      }

      const receiverRole = receiver.role;

      // Check if users can message each other
      const canMessage = await this.validateMessagingPermission(senderId, receiverId, senderRole, receiverRole);
      if (!canMessage) {
        socket.emit('message_error', { error: 'You are not authorized to message this user' });
        return;
      }

      // Find or create conversation
      let conversation;
      if (senderRole === 'doctor') {
        conversation = await Conversation.findOrCreateConversation(senderId, receiverId);
      } else {
        conversation = await Conversation.findOrCreateConversation(receiverId, senderId);
      }

      // Create the message
      const message = new ChatMessage({
        conversationId: conversation._id,
        sender: senderId,
        senderRole,
        receiver: receiverId,
        receiverRole,
        content: content.trim(),
        messageType,
        replyTo: replyTo || undefined
      });

      await message.save();

      // Populate message for response
      await message.populate([
        { path: 'sender', select: 'name email role' },
        { path: 'receiver', select: 'name email role' },
        { path: 'replyTo', select: 'content sender sentAt' }
      ]);

      // Update conversation unread count
      await conversation.incrementUnreadCount(senderRole);

      // Emit to sender (confirmation)
      socket.emit('message_sent', {
        message: message.toObject(),
        conversationId: conversation._id
      });

      // Emit to receiver if online
      const receiverSocketId = this.connectedUsers.get(receiverId);
      if (receiverSocketId) {
        this.io.to(receiverSocketId).emit('new_message', {
          message: message.toObject(),
          conversationId: conversation._id
        });
        
        // Mark as delivered
        await message.markAsDelivered();
      }

      // Emit to conversation room (for multiple device support)
      socket.to(`conversation_${conversation._id}`).emit('conversation_updated', {
        conversationId: conversation._id,
        lastMessage: message.toObject()
      });

    } catch (error) {
      console.error('Error sending message:', error);
      socket.emit('message_error', { error: 'Failed to send message' });
    }
  }

  // Handle joining a conversation
  async handleJoinConversation(socket, data) {
    try {
      const { conversationId } = data;
      const userId = socket.userId;

      // Verify user is part of this conversation
      const conversation = await Conversation.findById(conversationId);
      if (!conversation) {
        socket.emit('conversation_error', { error: 'Conversation not found' });
        return;
      }

      const isParticipant = conversation.participants.some(p => p.user.toString() === userId);
      if (!isParticipant) {
        socket.emit('conversation_error', { error: 'Not authorized to join this conversation' });
        return;
      }

      // Join conversation room
      socket.join(`conversation_${conversationId}`);
      
      // Mark messages as read
      await ChatMessage.markMessagesAsRead(conversationId, userId);
      
      // Reset unread count for this user
      await conversation.resetUnreadCount(socket.userRole);

      socket.emit('conversation_joined', { conversationId });

    } catch (error) {
      console.error('Error joining conversation:', error);
      socket.emit('conversation_error', { error: 'Failed to join conversation' });
    }
  }

  // Handle leaving a conversation
  handleLeaveConversation(socket, data) {
    const { conversationId } = data;
    socket.leave(`conversation_${conversationId}`);
    socket.emit('conversation_left', { conversationId });
  }

  // Handle marking messages as read
  async handleMarkAsRead(socket, data) {
    try {
      const { messageIds } = data;
      const userId = socket.userId;

      if (!Array.isArray(messageIds) || messageIds.length === 0) {
        return;
      }

      // Update messages
      await ChatMessage.updateMany(
        { 
          _id: { $in: messageIds }, 
          receiver: userId,
          status: { $ne: 'read' }
        },
        { 
          status: 'read', 
          readAt: new Date() 
        }
      );

      // Emit read receipts to senders
      const messages = await ChatMessage.find({ _id: { $in: messageIds } });
      
      messages.forEach(message => {
        const senderSocketId = this.connectedUsers.get(message.sender.toString());
        if (senderSocketId) {
          this.io.to(senderSocketId).emit('message_read', {
            messageId: message._id,
            readAt: new Date()
          });
        }
      });

    } catch (error) {
      console.error('Error marking messages as read:', error);
    }
  }

  // Validate messaging permission between users
  async validateMessagingPermission(senderId, receiverId, senderRole, receiverRole) {
    try {
      // If both are doctors or both are patients, they can't message each other
      if (senderRole === receiverRole) {
        return false;
      }

      // Check doctor-patient relationship using the Patient model
      if (senderRole === 'doctor') {
        // Check if the receiver (patient user) has a patient record assigned to this doctor
        const receiverUser = await User.findById(receiverId);
        if (!receiverUser) return false;

        const patient = await Patient.findOne({ 'contact.email': receiverUser.email });
        return patient && patient.assignedDoctorIds.includes(senderId);
      } else if (senderRole === 'patient') {
        // Check if the sender (patient user) has a patient record assigned to the receiver (doctor)
        const senderUser = await User.findById(senderId);
        if (!senderUser) return false;

        const patient = await Patient.findOne({ 'contact.email': senderUser.email });
        return patient && patient.assignedDoctorIds.includes(receiverId);
      }

      return false;
    } catch (error) {
      console.error('Error validating messaging permission:', error);
      return false;
    }
  }

  // Send user's conversations
  async sendUserConversations(socket) {
    try {
      const userId = socket.userId;
      const userRole = socket.userRole;

      let conversations;
      if (userRole === 'doctor') {
        conversations = await Conversation.find({ doctorId: userId })
          .populate('patientId', 'name email')
          .sort({ updatedAt: -1 });
      } else if (userRole === 'patient') {
        conversations = await Conversation.find({ patientId: userId })
          .populate('doctorId', 'name email')
          .sort({ updatedAt: -1 });
      }

      socket.emit('conversations_list', { conversations: conversations || [] });
    } catch (error) {
      console.error('Error sending user conversations:', error);
    }
  }

  // Broadcast user online/offline status
  async broadcastUserStatus(userId, userRole, status) {
    try {
      // Get users who should be notified about this user's status
      let relevantUsers = [];

      if (userRole === 'doctor') {
        // Notify all patients of this doctor
        const patients = await Patient.find({ assignedDoctorIds: userId });
        relevantUsers = patients.map(p => p._id.toString());
      } else if (userRole === 'patient') {
        // Notify all doctors of this patient
        const patient = await Patient.findById(userId).populate('assignedDoctorIds');
        if (patient && patient.assignedDoctorIds) {
          relevantUsers = patient.assignedDoctorIds.map(d => d._id.toString());
        }
      }

      // Emit status to relevant users
      relevantUsers.forEach(targetUserId => {
        const targetSocketId = this.connectedUsers.get(targetUserId);
        if (targetSocketId) {
          this.io.to(targetSocketId).emit('user_status_changed', {
            userId,
            status,
            timestamp: new Date()
          });
        }
      });

    } catch (error) {
      console.error('Error broadcasting user status:', error);
    }
  }

  // Get online users for a specific user
  getOnlineUsersFor(socket) {
    const userId = socket.userId;
    const userRole = socket.userRole;
    
    // This would return online status of relevant users
    // Implementation depends on your specific requirements
    socket.emit('online_users', { users: [] });
  }
}

module.exports = MessageHandlers;
