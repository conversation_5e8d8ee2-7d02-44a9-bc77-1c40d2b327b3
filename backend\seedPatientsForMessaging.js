const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
require('dotenv').config();

// Import models
const User = require('./models/User');
const Patient = require('./models/doctor/Patient');

const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('MongoDB connected for seeding patients!');
  } catch (error) {
    console.log('MongoDB connection failed:', error);
    process.exit(1);
  }
};

const seedPatientsForMessaging = async () => {
  try {
    console.log('Starting to seed patients for messaging...');

    // Get all doctors and patients from User model
    const doctors = await User.find({ role: 'doctor' });
    const patientUsers = await User.find({ role: 'patient' });

    console.log(`Found ${doctors.length} doctors and ${patientUsers.length} patient users`);

    if (doctors.length === 0) {
      console.log('No doctors found. Please run the user seeding script first.');
      return;
    }

    if (patientUsers.length === 0) {
      console.log('No patient users found. Please run the user seeding script first.');
      return;
    }

    // Create Patient records for each patient user and link them to doctors
    for (const patientUser of patientUsers) {
      // Check if patient record already exists
      let existingPatient = await Patient.findOne({ 
        'contact.email': patientUser.email 
      });

      if (!existingPatient) {
        // Create new patient record
        const newPatient = new Patient({
          name: patientUser.name,
          contact: {
            email: patientUser.email,
            phone: '555-0123' // Default phone
          },
          // Assign to random doctors (1-2 doctors per patient)
          assignedDoctorIds: doctors.slice(0, Math.floor(Math.random() * 2) + 1).map(d => d._id),
          createdAt: new Date()
        });

        await newPatient.save();
        console.log(`Created patient record for: ${patientUser.name} -> assigned to ${newPatient.assignedDoctorIds.length} doctor(s)`);
      } else {
        // Update existing patient with doctor assignments if needed
        if (!existingPatient.assignedDoctorIds || existingPatient.assignedDoctorIds.length === 0) {
          existingPatient.assignedDoctorIds = doctors.slice(0, Math.floor(Math.random() * 2) + 1).map(d => d._id);
          await existingPatient.save();
          console.log(`Updated patient record for: ${patientUser.name} -> assigned to ${existingPatient.assignedDoctorIds.length} doctor(s)`);
        } else {
          console.log(`Patient record already exists for: ${patientUser.name}`);
        }
      }
    }

    // Display the relationships
    console.log('\n=== Doctor-Patient Relationships ===');
    for (const doctor of doctors) {
      const assignedPatients = await Patient.find({ 
        assignedDoctorIds: doctor._id 
      });
      
      console.log(`\nDr. ${doctor.name} (${doctor.email}):`);
      if (assignedPatients.length > 0) {
        assignedPatients.forEach(patient => {
          console.log(`  - ${patient.name} (${patient.contact.email})`);
        });
      } else {
        console.log('  - No patients assigned');
      }
    }

    console.log('\n=== Patient-Doctor Relationships ===');
    for (const patientUser of patientUsers) {
      const patientRecord = await Patient.findOne({ 
        'contact.email': patientUser.email 
      }).populate('assignedDoctorIds', 'name email');
      
      console.log(`\n${patientUser.name} (${patientUser.email}):`);
      if (patientRecord && patientRecord.assignedDoctorIds.length > 0) {
        patientRecord.assignedDoctorIds.forEach(doctor => {
          console.log(`  - Dr. ${doctor.name} (${doctor.email})`);
        });
      } else {
        console.log('  - No doctors assigned');
      }
    }

    console.log('\n✅ Patient-Doctor relationships seeded successfully!');
    console.log('\nYou can now test messaging between doctors and their assigned patients.');

  } catch (error) {
    console.error('Error seeding patients:', error);
  } finally {
    mongoose.connection.close();
  }
};

const runSeed = async () => {
  await connectDB();
  await seedPatientsForMessaging();
};

runSeed();
