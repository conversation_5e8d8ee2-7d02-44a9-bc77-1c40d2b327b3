import React, { createContext, useContext, useEffect, useState, useRef } from 'react';
import { io, Socket } from 'socket.io-client';
import { useAuth } from './AuthContext';
import { toast } from '@/hooks/use-toast';

interface Message {
  id: string;
  conversationId: string;
  sender: {
    _id: string;
    name: string;
    email: string;
    role: string;
  };
  receiver: {
    _id: string;
    name: string;
    email: string;
    role: string;
  };
  content: string;
  messageType: 'text' | 'image' | 'file' | 'system';
  status: 'sent' | 'delivered' | 'read';
  sentAt: string;
  deliveredAt?: string;
  readAt?: string;
  replyTo?: any;
}

interface Conversation {
  _id: string;
  doctorId: any;
  patientId: any;
  lastMessage?: {
    content: string;
    sender: string;
    timestamp: string;
  };
  unreadCounts: {
    doctor: number;
    patient: number;
  };
  createdAt: string;
  updatedAt: string;
}

interface SocketContextType {
  socket: Socket | null;
  isConnected: boolean;
  conversations: Conversation[];
  onlineUsers: Set<string>;
  sendMessage: (receiverId: string, content: string, messageType?: string, replyTo?: string) => void;
  joinConversation: (conversationId: string) => void;
  leaveConversation: (conversationId: string) => void;
  markAsRead: (messageIds: string[]) => void;
  typingStart: (conversationId: string) => void;
  typingStop: (conversationId: string) => void;
}

const SocketContext = createContext<SocketContextType | undefined>(undefined);

export const useSocket = () => {
  const context = useContext(SocketContext);
  if (context === undefined) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};

interface SocketProviderProps {
  children: React.ReactNode;
}

export const SocketProvider: React.FC<SocketProviderProps> = ({ children }) => {
  const { user, token } = useAuth();
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [onlineUsers, setOnlineUsers] = useState<Set<string>>(new Set());
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (user && token) {
      connectSocket();
    } else {
      disconnectSocket();
    }

    return () => {
      disconnectSocket();
    };
  }, [user, token]);

  const connectSocket = () => {
    if (socket?.connected || !token) return;

    const newSocket = io(import.meta.env.VITE_API_URL || 'http://localhost:5000', {
      auth: {
        token: token
      },
      transports: ['websocket', 'polling'],
      timeout: 20000,
      forceNew: true
    });

    newSocket.on('connect', () => {
      console.log('Socket connected:', newSocket.id);
      setIsConnected(true);
      
      // Clear any reconnection timeout
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    });

    newSocket.on('disconnect', (reason) => {
      console.log('Socket disconnected:', reason);
      setIsConnected(false);
      
      // Attempt to reconnect after a delay
      if (reason === 'io server disconnect') {
        // Server initiated disconnect, don't reconnect automatically
        return;
      }
      
      reconnectTimeoutRef.current = setTimeout(() => {
        if (user && token) {
          connectSocket();
        }
      }, 3000);
    });

    newSocket.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
      setIsConnected(false);

      // Don't show error toast for authentication errors
      if (error.message !== 'Authentication error: No token provided' &&
          error.message !== 'Authentication error: Invalid token') {
        toast({
          title: "Connection Error",
          description: "Failed to connect to messaging service. Retrying...",
          variant: "destructive"
        });
      }
    });

    // Message events
    newSocket.on('new_message', (data: { message: Message; conversationId: string }) => {
      console.log('New message received:', data);
      
      // Update conversations list
      setConversations(prev => 
        prev.map(conv => 
          conv._id === data.conversationId 
            ? {
                ...conv,
                lastMessage: {
                  content: data.message.content,
                  sender: data.message.sender._id,
                  timestamp: data.message.sentAt
                },
                updatedAt: data.message.sentAt
              }
            : conv
        )
      );

      // Show notification if not in the conversation
      toast({
        title: `New message from ${data.message.sender.name}`,
        description: data.message.content.substring(0, 100) + (data.message.content.length > 100 ? '...' : ''),
      });
    });

    newSocket.on('message_sent', (data: { message: Message; conversationId: string }) => {
      console.log('Message sent confirmation:', data);
    });

    newSocket.on('message_read', (data: { messageId: string; readAt: string }) => {
      console.log('Message read receipt:', data);
    });

    newSocket.on('conversations_list', (data: { conversations: Conversation[] }) => {
      console.log('Conversations list received:', data);
      setConversations(data.conversations);
    });

    newSocket.on('conversation_joined', (data: { conversationId: string }) => {
      console.log('Joined conversation:', data.conversationId);
    });

    newSocket.on('conversation_left', (data: { conversationId: string }) => {
      console.log('Left conversation:', data.conversationId);
    });

    newSocket.on('user_status_changed', (data: { userId: string; status: 'online' | 'offline' }) => {
      setOnlineUsers(prev => {
        const newSet = new Set(prev);
        if (data.status === 'online') {
          newSet.add(data.userId);
        } else {
          newSet.delete(data.userId);
        }
        return newSet;
      });
    });

    newSocket.on('user_typing', (data: { userId: string; userName: string; conversationId: string }) => {
      console.log(`${data.userName} is typing in conversation ${data.conversationId}`);
    });

    newSocket.on('user_stopped_typing', (data: { userId: string; conversationId: string }) => {
      console.log(`User stopped typing in conversation ${data.conversationId}`);
    });

    newSocket.on('online_users', (data: { users: any[] }) => {
      setOnlineUsers(new Set(data.users.map(u => u.userId)));
    });

    // Error events
    newSocket.on('message_error', (data: { error: string }) => {
      toast({
        title: "Message Error",
        description: data.error,
        variant: "destructive"
      });
    });

    newSocket.on('conversation_error', (data: { error: string }) => {
      toast({
        title: "Conversation Error",
        description: data.error,
        variant: "destructive"
      });
    });

    setSocket(newSocket);
  };

  const disconnectSocket = () => {
    if (socket) {
      socket.disconnect();
      setSocket(null);
      setIsConnected(false);
      setConversations([]);
      setOnlineUsers(new Set());
    }
    
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
  };

  const sendMessage = (receiverId: string, content: string, messageType = 'text', replyTo?: string) => {
    if (!socket || !isConnected) {
      toast({
        title: "Connection Error",
        description: "Not connected to messaging service",
        variant: "destructive"
      });
      return;
    }

    socket.emit('send_message', {
      receiverId,
      content,
      messageType,
      replyTo
    });
  };

  const joinConversation = (conversationId: string) => {
    if (socket && isConnected) {
      socket.emit('join_conversation', { conversationId });
    }
  };

  const leaveConversation = (conversationId: string) => {
    if (socket && isConnected) {
      socket.emit('leave_conversation', { conversationId });
    }
  };

  const markAsRead = (messageIds: string[]) => {
    if (socket && isConnected) {
      socket.emit('mark_as_read', { messageIds });
    }
  };

  const typingStart = (conversationId: string) => {
    if (socket && isConnected) {
      socket.emit('typing_start', { conversationId });
    }
  };

  const typingStop = (conversationId: string) => {
    if (socket && isConnected) {
      socket.emit('typing_stop', { conversationId });
    }
  };

  const value: SocketContextType = {
    socket,
    isConnected,
    conversations,
    onlineUsers,
    sendMessage,
    joinConversation,
    leaveConversation,
    markAsRead,
    typingStart,
    typingStop
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
};
