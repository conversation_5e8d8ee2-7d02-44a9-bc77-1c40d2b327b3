const express = require('express');
const router = express.Router();
const { protect } = require('../middleware/authMiddleware');
const {
  getConversations,
  getConversationMessages,
  sendMessage,
  getAvailableContacts,
  createOrGetConversation
} = require('../controllers/messageController');

// Apply authentication middleware to all routes
router.use(protect);

// Get all conversations for the authenticated user
router.get('/conversations', getConversations);

// Get messages for a specific conversation (between two users)
router.get('/conversations/:otherUserId/messages', getConversationMessages);

// Send a message (REST API fallback)
router.post('/send', sendMessage);

// Get available contacts for messaging
router.get('/contacts', getAvailableContacts);

// Create or get conversation with a specific user
router.post('/conversations', createOrGetConversation);

module.exports = router;
