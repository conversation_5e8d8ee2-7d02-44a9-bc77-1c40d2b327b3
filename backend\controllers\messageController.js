const Message = require('../models/doctor/Message');
const Patient = require('../models/doctor/Patient');
const User = require('../models/User');

// Get all conversations for the authenticated user
const getConversations = async (req, res) => {
  try {
    const userId = req.user._id;
    const userRole = req.user.role;

    let conversations = [];

    if (userRole === 'doctor') {
      // Get all messages where this doctor is the recipient
      const messages = await Message.find({ recipientId: userId })
        .populate('senderId', 'name email role')
        .sort({ timestamp: -1 });

      // Group by sender to create conversation-like structure
      const conversationMap = new Map();
      messages.forEach(msg => {
        const senderId = msg.senderId._id.toString();
        if (!conversationMap.has(senderId)) {
          conversationMap.set(senderId, {
            id: senderId,
            participant: msg.senderId,
            lastMessage: {
              content: msg.content,
              timestamp: msg.timestamp,
              isRead: msg.isRead
            },
            unreadCount: 0
          });
        }
        if (!msg.isRead) {
          conversationMap.get(senderId).unreadCount++;
        }
      });

      conversations = Array.from(conversationMap.values());
    } else if (userRole === 'patient') {
      // Get all messages where this patient is the sender
      const messages = await Message.find({ senderId: userId })
        .populate('recipientId', 'name email role')
        .sort({ timestamp: -1 });

      // Group by recipient to create conversation-like structure
      const conversationMap = new Map();
      messages.forEach(msg => {
        const recipientId = msg.recipientId._id.toString();
        if (!conversationMap.has(recipientId)) {
          conversationMap.set(recipientId, {
            id: recipientId,
            participant: msg.recipientId,
            lastMessage: {
              content: msg.content,
              timestamp: msg.timestamp,
              isRead: msg.isRead
            },
            unreadCount: 0
          });
        }
      });

      conversations = Array.from(conversationMap.values());
    } else {
      return res.status(403).json({ message: 'Access denied' });
    }

    res.json({
      success: true,
      conversations
    });

  } catch (error) {
    console.error('Error fetching conversations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch conversations'
    });
  }
};

// Get messages for a specific conversation (between two users)
const getConversationMessages = async (req, res) => {
  try {
    const { otherUserId } = req.params; // Changed from conversationId to otherUserId
    const { page = 1, limit = 50 } = req.query;
    const userId = req.user._id;

    // Validate other user exists
    const otherUser = await User.findById(otherUserId);
    if (!otherUser) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Validate messaging permission
    const canMessage = await validateMessagingPermission(userId, otherUserId, req.user.role, otherUser.role);
    if (!canMessage) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to this conversation'
      });
    }

    // Get messages between these two users
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const messages = await Message.find({
      $or: [
        { senderId: userId, recipientId: otherUserId },
        { senderId: otherUserId, recipientId: userId }
      ]
    })
    .populate('senderId', 'name email role')
    .populate('recipientId', 'name email role')
    .sort({ timestamp: -1 })
    .skip(skip)
    .limit(parseInt(limit));

    // Mark messages as read for the requesting user
    await Message.updateMany(
      {
        senderId: otherUserId,
        recipientId: userId,
        isRead: false
      },
      { isRead: true }
    );

    res.json({
      success: true,
      messages: messages.reverse(), // Reverse to show oldest first
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        hasMore: messages.length === parseInt(limit)
      }
    });

  } catch (error) {
    console.error('Error fetching conversation messages:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch messages'
    });
  }
};

// Send a message (REST API fallback)
const sendMessage = async (req, res) => {
  try {
    const { receiverId, content, subject } = req.body;
    const senderId = req.user._id;
    const senderRole = req.user.role;

    // Validate required fields
    if (!receiverId || !content?.trim()) {
      return res.status(400).json({
        success: false,
        message: 'Receiver ID and content are required'
      });
    }

    // Get receiver information
    const receiver = await User.findById(receiverId);
    if (!receiver) {
      return res.status(404).json({
        success: false,
        message: 'Receiver not found'
      });
    }

    const receiverRole = receiver.role;

    // Validate messaging permission
    const canMessage = await validateMessagingPermission(senderId, receiverId, senderRole, receiverRole);
    if (!canMessage) {
      return res.status(403).json({
        success: false,
        message: 'You are not authorized to message this user'
      });
    }

    // Create the message using existing Message model
    const message = new Message({
      senderId,
      senderName: req.user.name,
      recipientId: receiverId,
      subject: subject || '',
      content: content.trim(),
      timestamp: new Date(),
      isRead: false
    });

    await message.save();

    // Populate message for response
    await message.populate([
      { path: 'senderId', select: 'name email role' },
      { path: 'recipientId', select: 'name email role' }
    ]);

    res.status(201).json({
      success: true,
      message: message.toObject()
    });

  } catch (error) {
    console.error('Error sending message:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send message'
    });
  }
};

// Get available contacts for messaging
const getAvailableContacts = async (req, res) => {
  try {
    const userId = req.user._id;
    const userRole = req.user.role;

    let contacts = [];

    if (userRole === 'doctor') {
      // Get doctor's patients using the existing Patient model
      const patients = await Patient.find({
        assignedDoctorIds: userId
      }).select('name contact');

      // For each patient, find the corresponding User record to get the actual user ID
      const patientContacts = [];
      for (const patient of patients) {
        if (patient.contact?.email) {
          const patientUser = await User.findOne({
            email: patient.contact.email,
            role: 'patient'
          });
          if (patientUser) {
            patientContacts.push({
              id: patientUser._id, // Use the User ID, not Patient ID
              name: patient.name,
              email: patient.contact.email,
              role: 'patient',
              canMessage: true
            });
          }
        }
      }
      contacts = patientContacts;
    } else if (userRole === 'patient') {
      // For patients, we need to find which doctors they are assigned to
      // First, find the patient record for this user
      const patientRecord = await Patient.findOne({
        'contact.email': req.user.email
      }).populate('assignedDoctorIds', 'name email');

      if (patientRecord && patientRecord.assignedDoctorIds) {
        contacts = patientRecord.assignedDoctorIds.map(doctor => ({
          id: doctor._id,
          name: doctor.name,
          email: doctor.email,
          role: 'doctor',
          canMessage: true
        }));
      }
    }

    res.json({
      success: true,
      contacts
    });

  } catch (error) {
    console.error('Error fetching available contacts:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch contacts'
    });
  }
};

// Create or get conversation with a specific user
const createOrGetConversation = async (req, res) => {
  try {
    const { otherUserId } = req.body;
    const userId = req.user._id;
    const userRole = req.user.role;

    if (!otherUserId) {
      return res.status(400).json({
        success: false,
        message: 'Other user ID is required'
      });
    }

    // Get other user information
    const otherUser = await User.findById(otherUserId);
    if (!otherUser) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Validate messaging permission
    const canMessage = await validateMessagingPermission(userId, otherUserId, userRole, otherUser.role);
    if (!canMessage) {
      return res.status(403).json({
        success: false,
        message: 'You are not authorized to message this user'
      });
    }

    // Return conversation info (simplified for existing Message model)
    const conversation = {
      id: otherUserId,
      participant: {
        _id: otherUser._id,
        name: otherUser.name,
        email: otherUser.email,
        role: otherUser.role
      }
    };

    res.json({
      success: true,
      conversation
    });

  } catch (error) {
    console.error('Error creating/getting conversation:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create/get conversation'
    });
  }
};

// Helper function to validate messaging permission
const validateMessagingPermission = async (senderId, receiverId, senderRole, receiverRole) => {
  try {
    // If both are doctors or both are patients, they can't message each other
    if (senderRole === receiverRole) {
      return false;
    }

    // Check doctor-patient relationship using the Patient model
    if (senderRole === 'doctor') {
      // Check if the receiver (patient user) has a patient record assigned to this doctor
      const receiverUser = await User.findById(receiverId);
      if (!receiverUser) return false;

      const patient = await Patient.findOne({ 'contact.email': receiverUser.email });
      return patient && patient.assignedDoctorIds.includes(senderId);
    } else if (senderRole === 'patient') {
      // Check if the sender (patient user) has a patient record assigned to the receiver (doctor)
      const senderUser = await User.findById(senderId);
      if (!senderUser) return false;

      const patient = await Patient.findOne({ 'contact.email': senderUser.email });
      return patient && patient.assignedDoctorIds.includes(receiverId);
    }

    return false;
  } catch (error) {
    console.error('Error validating messaging permission:', error);
    return false;
  }
};

module.exports = {
  getConversations,
  getConversationMessages,
  sendMessage,
  getAvailableContacts,
  createOrGetConversation
};
