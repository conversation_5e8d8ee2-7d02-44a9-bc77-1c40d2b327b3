const ChatMessage = require('../models/ChatMessage');
const Conversation = require('../models/Conversation');
const DoctorPatientRelation = require('../models/DoctorPatientRelation');
const Patient = require('../models/doctor/Patient');
const User = require('../models/User');

// Get all conversations for the authenticated user
const getConversations = async (req, res) => {
  try {
    const userId = req.user._id;
    const userRole = req.user.role;

    let conversations;
    
    if (userRole === 'doctor') {
      conversations = await Conversation.find({ doctorId: userId })
        .populate('patientId', 'name email')
        .populate('lastMessage.sender', 'name')
        .sort({ updatedAt: -1 });
    } else if (userRole === 'patient') {
      conversations = await Conversation.find({ patientId: userId })
        .populate('doctorId', 'name email')
        .populate('lastMessage.sender', 'name')
        .sort({ updatedAt: -1 });
    } else {
      return res.status(403).json({ message: 'Access denied' });
    }

    res.json({
      success: true,
      conversations: conversations || []
    });

  } catch (error) {
    console.error('Error fetching conversations:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to fetch conversations' 
    });
  }
};

// Get messages for a specific conversation
const getConversationMessages = async (req, res) => {
  try {
    const { conversationId } = req.params;
    const { page = 1, limit = 50 } = req.query;
    const userId = req.user._id;

    // Verify user is part of this conversation
    const conversation = await Conversation.findById(conversationId);
    if (!conversation) {
      return res.status(404).json({ 
        success: false, 
        message: 'Conversation not found' 
      });
    }

    const isParticipant = conversation.doctorId.toString() === userId.toString() || 
                         conversation.patientId.toString() === userId.toString();
    
    if (!isParticipant) {
      return res.status(403).json({ 
        success: false, 
        message: 'Access denied to this conversation' 
      });
    }

    // Get messages with pagination
    const messages = await ChatMessage.getConversationMessages(
      conversationId, 
      parseInt(page), 
      parseInt(limit)
    );

    // Mark messages as read for the requesting user
    await ChatMessage.markMessagesAsRead(conversationId, userId);
    
    // Reset unread count for this user
    await conversation.resetUnreadCount(req.user.role);

    res.json({
      success: true,
      messages: messages.reverse(), // Reverse to show oldest first
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        hasMore: messages.length === parseInt(limit)
      }
    });

  } catch (error) {
    console.error('Error fetching conversation messages:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to fetch messages' 
    });
  }
};

// Send a message (REST API fallback)
const sendMessage = async (req, res) => {
  try {
    const { receiverId, content, messageType = 'text', replyTo } = req.body;
    const senderId = req.user._id;
    const senderRole = req.user.role;

    // Validate required fields
    if (!receiverId || !content?.trim()) {
      return res.status(400).json({ 
        success: false, 
        message: 'Receiver ID and content are required' 
      });
    }

    // Get receiver information
    const receiver = await User.findById(receiverId);
    if (!receiver) {
      return res.status(404).json({ 
        success: false, 
        message: 'Receiver not found' 
      });
    }

    const receiverRole = receiver.role;

    // Validate messaging permission
    const canMessage = await validateMessagingPermission(senderId, receiverId, senderRole, receiverRole);
    if (!canMessage) {
      return res.status(403).json({ 
        success: false, 
        message: 'You are not authorized to message this user' 
      });
    }

    // Find or create conversation
    let conversation;
    if (senderRole === 'doctor') {
      conversation = await Conversation.findOrCreateConversation(senderId, receiverId);
    } else {
      conversation = await Conversation.findOrCreateConversation(receiverId, senderId);
    }

    // Create the message
    const message = new ChatMessage({
      conversationId: conversation._id,
      sender: senderId,
      senderRole,
      receiver: receiverId,
      receiverRole,
      content: content.trim(),
      messageType,
      replyTo: replyTo || undefined
    });

    await message.save();

    // Populate message for response
    await message.populate([
      { path: 'sender', select: 'name email role' },
      { path: 'receiver', select: 'name email role' },
      { path: 'replyTo', select: 'content sender sentAt' }
    ]);

    // Update conversation unread count
    await conversation.incrementUnreadCount(senderRole);

    res.status(201).json({
      success: true,
      message: message.toObject(),
      conversationId: conversation._id
    });

  } catch (error) {
    console.error('Error sending message:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to send message' 
    });
  }
};

// Get available contacts for messaging
const getAvailableContacts = async (req, res) => {
  try {
    const userId = req.user._id;
    const userRole = req.user.role;

    let contacts = [];

    if (userRole === 'doctor') {
      // Get doctor's patients using the existing Patient model
      const patients = await Patient.find({
        assignedDoctorIds: userId
      }).select('name contact');

      contacts = patients.map(patient => ({
        id: patient._id,
        name: patient.name,
        email: patient.contact?.email || '',
        role: 'patient',
        canMessage: true
      }));
    } else if (userRole === 'patient') {
      // For patients, we need to find which doctors they are assigned to
      // First, find the patient record for this user
      const patientRecord = await Patient.findOne({
        'contact.email': req.user.email
      }).populate('assignedDoctorIds', 'name email');

      if (patientRecord && patientRecord.assignedDoctorIds) {
        contacts = patientRecord.assignedDoctorIds.map(doctor => ({
          id: doctor._id,
          name: doctor.name,
          email: doctor.email,
          role: 'doctor',
          canMessage: true
        }));
      }
    }

    res.json({
      success: true,
      contacts
    });

  } catch (error) {
    console.error('Error fetching available contacts:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch contacts'
    });
  }
};

// Create or get conversation with a specific user
const createOrGetConversation = async (req, res) => {
  try {
    const { otherUserId } = req.body;
    const userId = req.user._id;
    const userRole = req.user.role;

    if (!otherUserId) {
      return res.status(400).json({ 
        success: false, 
        message: 'Other user ID is required' 
      });
    }

    // Get other user information
    const otherUser = await User.findById(otherUserId);
    if (!otherUser) {
      return res.status(404).json({ 
        success: false, 
        message: 'User not found' 
      });
    }

    // Validate messaging permission
    const canMessage = await validateMessagingPermission(userId, otherUserId, userRole, otherUser.role);
    if (!canMessage) {
      return res.status(403).json({ 
        success: false, 
        message: 'You are not authorized to message this user' 
      });
    }

    // Find or create conversation
    let conversation;
    if (userRole === 'doctor') {
      conversation = await Conversation.findOrCreateConversation(userId, otherUserId);
    } else {
      conversation = await Conversation.findOrCreateConversation(otherUserId, userId);
    }

    // Populate conversation
    await conversation.populate([
      { path: 'doctorId', select: 'name email role' },
      { path: 'patientId', select: 'name email role' }
    ]);

    res.json({
      success: true,
      conversation
    });

  } catch (error) {
    console.error('Error creating/getting conversation:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to create/get conversation' 
    });
  }
};

// Helper function to validate messaging permission
const validateMessagingPermission = async (senderId, receiverId, senderRole, receiverRole) => {
  try {
    // If both are doctors or both are patients, they can't message each other
    if (senderRole === receiverRole) {
      return false;
    }

    // Check doctor-patient relationship using the Patient model
    if (senderRole === 'doctor') {
      // Check if the receiver (patient) is assigned to this doctor
      const patient = await Patient.findById(receiverId);
      return patient && patient.assignedDoctorIds.includes(senderId);
    } else if (senderRole === 'patient') {
      // Check if the sender (patient) is assigned to the receiver (doctor)
      const patient = await Patient.findById(senderId);
      return patient && patient.assignedDoctorIds.includes(receiverId);
    }

    return false;
  } catch (error) {
    console.error('Error validating messaging permission:', error);
    return false;
  }
};

module.exports = {
  getConversations,
  getConversationMessages,
  sendMessage,
  getAvailableContacts,
  createOrGetConversation
};
